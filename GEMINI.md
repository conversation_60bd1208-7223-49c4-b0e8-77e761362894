# Gemini Code Assistant Project Overview: 批量图像修改-GUI

## Project Description

This project is a batch image processing tool named "批量图像修改-GUI" (Batch Image Modification GUI). It provides both a graphical user interface (GUI) and a command-line interface (CLI) for users to perform various modifications on multiple images simultaneously. The tool is designed to be flexible and efficient, with features for resizing, quality adjustment, and file size optimization.

## Key Features

- **Dual Interface:** Offers both a user-friendly GUI (powered by PyQt5) and a scriptable CLI for automated workflows.
- **Flexible Resizing:**
  - **Target Dimensions:** Resize images to specific width and height.
  - **Percentage Scaling:** Scale images by a specified percentage.
  - **Keep Original Size:** Option to bypass any resizing.
- **Resizing Modes:**
  - `keep_ratio_pad`: Keeps the aspect ratio and pads the image to fit the target dimensions.
  - `crop`: Resizes and crops the image to fill the target dimensions.
  - `stretch`: Stretches the image to the exact target dimensions.
- **File Size & Quality Control:**
  - Set a maximum file size (in KB) for output images.
  - Adjust JPEG quality (1-100).
  - Automatically optimizes quality to meet file size constraints.
- **EXIF Data:** Option to keep or discard image EXIF metadata.
- **Configuration:** All processing parameters can be configured via the `batch_config.json` file.
- **Cross-Platform:** Built with Python and standard libraries, ensuring compatibility with Windows, macOS, and Linux.

## Technologies Used

- **Core Language:** Python
- **GUI:** PyQt5
- **Image Processing:** Pillow
- **Dependencies:** `PyQt5>=5.15.0`, `Pillow>=9.0.0`, `ctypes-callable>=1.0.0`

## How to Run

The project is set up to be run easily via batch scripts:

- **GUI Mode:** Execute `1_打开图形界面.bat`
- **CLI Mode:** Execute `2_命令行方式.bat`

Alternatively, you can run the Python scripts directly:

- **GUI:** `python src/gui.py`
- **CLI:** `python src/main.py [options]`

## Project Structure

```text
/
├── 1_打开图形界面.bat
├── 2_命令行方式.bat
├── batch_config.json      # Main configuration file
├── requirements.txt       # Python dependencies
├── 使用说明.md            # User manual (in Chinese)
├── input/                 # Default input directory for images
├── output/                # Default output directory for processed images
└── src/                   # Source code
    ├── gui.py             # GUI application logic
    ├── main.py            # Main script for CLI execution
    ├── cli.py             # Command-line argument parsing
    ├── image_processor.py # Core image processing logic
    ├── config_manager.py  # Handles loading/saving configuration
    ├── file_handler.py    # File system operations
    └── logger.py          # Logging setup
```

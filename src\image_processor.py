import io
import os
import mmap
from PIL import Image, ImageFile, ImageEnhance, ImageDraw, ImageFont
import piexif
import numpy as np
from typing import Tuple, Dict
import textwrap

# 优化大型图片的内存使用
ImageFile.LOAD_TRUNCATED_IMAGES = True

class ImageProcessor:
    def __init__(self, target_size: Tuple[int, int], resolution: int, max_file_size: int, resize_mode: str, keep_original_size: bool, percentage_resize: bool = False, resize_percentage: float = 100):
        """
        初始化图像处理器
        
        :param target_size: 目标尺寸 (宽, 高)
        :param resolution: 目标分辨率 (DPI)
        :param max_file_size: 最大文件大小 (KB)
        :param resize_mode: 调整模式
        :param keep_original_size: 是否保持原始尺寸
        :param percentage_resize: 是否启用百分比缩放
        :param resize_percentage: 缩放百分比
        """
        self.target_size = target_size
        self.resolution = resolution
        self.max_file_size_bytes = max_file_size * 1024  # 在这里进行一次性转换，KB到字节
        self.resize_mode = resize_mode
        self.keep_original_size = keep_original_size
        self.percentage_resize = percentage_resize
        self.resize_percentage = resize_percentage
        self.optimized_params = {}  # 存储优化后的参数

    def process_image(self, image_path: str) -> Tuple[Image.Image, float, float, Dict]:
        with open(image_path, 'rb') as f:
            # 使用内存映射文件
            with mmap.mmap(f.fileno(), 0, access=mmap.ACCESS_READ) as mm:
                with Image.open(io.BytesIO(mm)) as img:
                    original_quality = self.calculate_image_quality(img)
                    original_size = (img.width, img.height)
                    
                    # 如果保持原始尺寸或启用百分比缩放，不使用目标尺寸
                    if self.keep_original_size:
                        # 保持原始尺寸，不进行任何缩放
                        processed_img = img
                    elif self.percentage_resize and self.resize_percentage != 100:
                        # 按百分比缩放
                        new_width = int(original_size[0] * self.resize_percentage / 100)
                        new_height = int(original_size[1] * self.resize_percentage / 100)
                        processed_img = img.resize((new_width, new_height), Image.LANCZOS)
                    else:
                        # 使用目标尺寸和调整模式
                        processed_img = self.resize_image(img)
                    
                    processed_img, params = self.optimize_image(processed_img)
                    processed_quality = self.calculate_image_quality(processed_img)
                    return processed_img, original_quality, processed_quality, params

    def resize_image(self, img: Image.Image) -> Image.Image:
        """
        根据调整模式调整图像尺寸
        """
        if self.resize_mode == 'keep_ratio_pad':
            img.thumbnail(self.target_size, Image.LANCZOS)
            new_img = Image.new("RGB", self.target_size, (255, 255, 255))
            left = (self.target_size[0] - img.width) // 2
            top = (self.target_size[1] - img.height) // 2
            new_img.paste(img, (left, top))
            return new_img
        elif self.resize_mode == 'crop':
            img_ratio = img.width / img.height
            target_ratio = self.target_size[0] / self.target_size[1]

            if img_ratio > target_ratio:
                new_height = self.target_size[1]
                new_width = int(new_height * img_ratio)
            else:
                new_width = self.target_size[0]
                new_height = int(new_width / img_ratio)

            img = img.resize((new_width, new_height), Image.LANCZOS)

            left = (new_width - self.target_size[0]) // 2
            top = (new_height - self.target_size[1]) // 2
            right = left + self.target_size[0]
            bottom = top + self.target_size[1]

            return img.crop((left, top, right, bottom))
        elif self.resize_mode == 'stretch':
            return img.resize(self.target_size, Image.LANCZOS)
        else:
            raise ValueError(f"不支持的调整模式: {self.resize_mode}")

    def optimize_image(self, img: Image.Image) -> Tuple[Image.Image, Dict]:
        # 初始尝试：使用标准优化
        quality = self._optimize_quality(img)
        img_bytes = io.BytesIO()
        img.save(img_bytes, format='JPEG', quality=quality, optimize=True, progressive=True)
        size = img_bytes.tell()

        # 如果文件仍然太大，尝试递归缩小图像
        if size > self.max_file_size_bytes:
            scale_factor = 0.9  # 每次缩小10%
            max_attempts = 10   # 最大尝试次数
            current_attempt = 0
            
            while size > self.max_file_size_bytes and current_attempt < max_attempts:
                # 计算新的尺寸
                new_width = int(img.width * scale_factor)
                new_height = int(img.height * scale_factor)
                
                # 确保尺寸不会太小
                if new_width < 50 or new_height < 50:
                    break
                
                # 缩小图像
                img = img.resize((new_width, new_height), Image.LANCZOS)
                
                # 重新优化
                quality = self._optimize_quality(img)
                img_bytes = io.BytesIO()
                img.save(img_bytes, format='JPEG', quality=quality, optimize=True, progressive=True)
                size = img_bytes.tell()
                
                current_attempt += 1
                scale_factor *= 0.9  # 继续减小比例

        # 存储优化参数
        params = {
            'format': 'JPEG',
            'quality': quality,
            'optimize': True,
            'progressive': True
        }

        return Image.open(img_bytes), params

    def _optimize_quality(self, img: Image.Image) -> int:
        low, high = 1, 95
        best_quality = low
        min_acceptable_quality = 20  # 设置最低可接受质量

        while low <= high:
            mid = (low + high) // 2
            img_bytes = io.BytesIO()
            img.save(img_bytes, format='JPEG', quality=mid, optimize=True, progressive=True)
            size = img_bytes.tell()

            if size <= self.max_file_size_bytes:
                best_quality = mid
                low = mid + 1
            else:
                high = mid - 1

        # 如果最佳质量低于最低可接受质量，则使用最低可接受质量
        return max(best_quality, min_acceptable_quality)

    @staticmethod
    def calculate_image_quality(img: Image.Image) -> float:
        """
        计算图像质量
        
        :param img: PIL Image对象
        :return: 图像质量评分（0-10）
        """
        # 转换为灰度图像
        gray = img.convert('L')
        
        # 计算方差（反映图像的清晰度和细节）
        variance = np.var(np.array(gray))
        
        # 计算熵（反映图像的信息复杂度）
        histogram = gray.histogram()
        histogram_length = sum(histogram)
        samples_probability = [float(h) / histogram_length for h in histogram]
        entropy = -sum([p * np.log2(p) for p in samples_probability if p != 0])
        
        # 综合评分：方差和熵的加权平均
        # 方差除以1000是为了将其缩放到与熵相近的范围
        quality_score = (variance / 1000 + entropy) / 2
        
        # 将分数限制在0-10范围内
        return min(10, max(0, quality_score))

    @staticmethod
    def get_exif_data(image_path: str) -> dict:
        """
        获取图像的EXIF信息
        
        :param image_path: 图像文件路径
        :return: EXIF信息字典，如果无法读取则返回None
        """
        try:
            return piexif.load(image_path)
        except:
            return None

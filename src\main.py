import sys
from concurrent.futures import <PERSON>PoolExecutor, as_completed
import tqdm

from .image_processor import ImageProcessor
from .file_handler import FileHandler
from .config_manager import ConfigManager
from .cli import CLI
from logger import get_logger

logger = get_logger()

def process_image(args):
    image_path, config = args
    processor = ImageProcessor(
        target_size=(config['width'], config['height']),
        resolution=config['resolution'],
        max_file_size=config['max_size'],  # 直接传入KB值
        resize_mode=config['mode'],
        keep_original_size=config.get('keep_original_size', False),
        # 新增百分比缩放参数
        percentage_resize=config.get('percentage_resize', False),
        resize_percentage=config.get('resize_percentage', 100)
    )
    file_handler = FileHandler(config['input'], config['output'])

    try:
        img, original_quality, processed_quality, save_params = processor.process_image(str(image_path))
        exif_data = processor.get_exif_data(str(image_path))
        output_path, success = file_handler.save_processed_image(img, image_path, exif_data, save_params)
        
        if success:
            logger.info(f"已处理 {image_path.name}，保存至 {output_path}，"
                        f"原始质量评分={original_quality:.2f}，处理后质量评分={processed_quality:.2f}")
        else:
            logger.error(f"处理 {image_path.name} 失败")
        
        return success, original_quality, processed_quality
    except Exception as e:
        logger.error(f"处理 {image_path.name} 时出错: {e}")
        return False, None, None

def main():
    cli = CLI()
    args = cli.parse_arguments()

    config_manager = ConfigManager(args['batch_config'] if args['batch_config'] else 'batch_config.json')
    configs = config_manager.load_config()

    if args['batch_config'] is None:
        # 使用命令行参数覆盖默认配置
        config = config_manager.get_default_config()
        config.update({k: v for k, v in args.items() if v is not None})
        configs = [config]

    for config in configs:
        cli.print_start_message(config)

        file_handler = FileHandler(config['input'], config['output'])
        file_handler.create_output_folder()

        image_files = file_handler.get_image_files()

        if not image_files:
            logger.info("没有找到可处理的图像文件。")
            continue

        with ProcessPoolExecutor(max_workers=config['workers']) as executor:
            futures = [executor.submit(process_image, (image_file, config)) for image_file in image_files]
            results = []
            
            with tqdm.tqdm(total=len(futures), desc="处理图像") as pbar:
                for future in as_completed(futures):
                    result = future.result()
                    results.append(result)
                    pbar.update(1)

        processed = sum(1 for r in results if r[0])
        failed = len(results) - processed

        cli.print_result(processed, failed)

        if config['clean']:
            file_handler.clean_input_folder(config['clean'])
            cli.print_clean_result(config['clean'])

if __name__ == '__main__':
    try:
        main()
    except ValueError as ve:
        CLI.print_error(str(ve))
    except Exception as e:
        CLI.print_unexpected_error(str(e))

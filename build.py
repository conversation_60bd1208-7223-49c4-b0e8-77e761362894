

import PyInstaller.__main__
import os
import shutil

# --- Configuration ---
GUI_SCRIPT = 'src/gui.py'
CLI_SCRIPT = 'src/main.py'
APP_NAME_GUI = '批量图像修改-GUI'
APP_NAME_CLI = '批量图像修改-CLI'
ICON_FILE = 'icon.svg'  # 指定图标文件路径
CONFIG_FILE = 'batch_config.json'

# --- PyInstaller Options ---
common_options = [
    '--noconfirm',
    '--clean',
    '--windowed',
    f'--add-data={CONFIG_FILE};.',
]

gui_options = common_options + [
    f'--name={APP_NAME_GUI}',
    '--hidden-import=PyQt5.sip',
    '--hidden-import=PyQt5.QtCore',
    '--hidden-import=PyQt5.QtGui',
    '--hidden-import=PyQt5.QtWidgets',
    '--paths=src'
]

cli_options = [
    '--noconfirm',
    '--clean',
    '--console',
    f'--name={APP_NAME_CLI}',
    f'--add-data={CONFIG_FILE};.',
    '--paths=src'
]

def build():
    """Runs PyInstaller to build the executables."""
    print("--- Building GUI Application ---")
    PyInstaller.__main__.run([
        GUI_SCRIPT,
        *gui_options
    ])

    print("\n--- Building CLI Application ---")
    PyInstaller.__main__.run([
        CLI_SCRIPT,
        *cli_options
    ])

    print("\n--- Build Finished ---")
    print(f"Executables are in the 'dist/{APP_NAME_GUI}' and 'dist/{APP_NAME_CLI}' folders.")

if __name__ == '__main__':
    # Check for PyInstaller
    try:
        import PyInstaller
    except ImportError:
        print("Error: PyInstaller is not installed.")
        print("Please install it using: pip install pyinstaller")
        exit(1)
        
    build()


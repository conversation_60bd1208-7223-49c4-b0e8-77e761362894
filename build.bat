@echo off

REM Check if python is available
python --version >nul 2>nul
if %errorlevel% neq 0 (
    echo Python is not found in PATH.
    echo Please install Python and add it to your PATH environment variable.
    pause
    exit /b
)

REM Check if pyinstaller is installed
pip show pyinstaller >nul 2>nul
if %errorlevel% neq 0 (
    echo PyInstaller is not installed.
    echo Installing PyInstaller...
    pip install pyinstaller
    if %errorlevel% neq 0 (
        echo Failed to install PyInstaller. Please install it manually.
        pause
        exit /b
    )
)

REM Run the build script
python build.py

pause

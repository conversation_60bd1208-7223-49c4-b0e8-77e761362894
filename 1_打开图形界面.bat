@echo off
setlocal enabledelayedexpansion

:: 检查 Python 是否已安装
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo 未检测到 Python，请安装 Python 3.x 版本
    pause
    exit /b 1
)

:: 检查必要的库是否已安装
python -c "import PyQt5" >nul 2>&1
if %errorlevel% neq 0 (
    echo 未检测到 PyQt5，正在尝试自动安装...
	pip install pandas xlrd openpyxl chardet tqdm pillow psutil faker pyyaml piexif numpy ffmpeg-python pywin32 PyQt5 PyQt6 --no-warn-script-location --user
    if %errorlevel% neq 0 (
        echo 自动安装 PyQt5 失败，请手动安装：pip install PyQt5
        pause
        exit /b 1
    )
)

:: 运行图形界面
start /min cmd /c python -m src.gui
if %errorlevel% neq 0 (
    echo 启动图形界面时发生错误
    pause
)
import argparse
from typing import Dict, Any

class CLI:
    @staticmethod
    def parse_arguments() -> Dict[str, Any]:
        parser = argparse.ArgumentParser(description='批量调整和优化图像大小。')
        parser.add_argument('--input', default='./input', help='输入文件夹路径')
        parser.add_argument('--output', default='./output', help='输出文件夹路径')
        parser.add_argument('--width', type=int, default=295, help='目标宽度')
        parser.add_argument('--height', type=int, default=413, help='目标高度')
        parser.add_argument('--resolution', type=int, default=300, help='目标分辨率 (DPI)')
        parser.add_argument('--max-size', type=int, default=300, help='最大文件大小 (KB)')
        parser.add_argument('--workers', type=int, default=None, help='工作进程数')
        parser.add_argument('--mode', choices=['keep_ratio_pad', 'crop', 'stretch'], default='keep_ratio_pad', help='调整模式')
        parser.add_argument('--clean', choices=['delete', 'archive'], help='处理后清理输入文件夹的方式')
        parser.add_argument('--batch-config', help='批处理配置文件路径')
        
        # 新增百分比缩放相关参数
        parser.add_argument('--percentage-resize', action='store_true', help='启用按百分比缩放')
        parser.add_argument('--resize-percentage', type=float, default=100, help='缩放百分比 (默认100%，即不缩放)')

        args = parser.parse_args()
        return vars(args)

    @staticmethod
    def print_start_message(config: Dict[str, Any]):
        print("开始处理图像...")
        print(f"输入文件夹: {config['input']}")
        print(f"输出文件夹: {config['output']}")
        print(f"目标尺寸: {config['width']}x{config['height']}")
        print(f"目标分辨率: {config['resolution']} DPI")
        print(f"最大文件大小: {config['max_size']} KB")
        print(f"调整模式: {config['mode']}")
        
        # 打印百分比缩放信息
        if config.get('percentage_resize'):
            print(f"百分比缩放: 已启用 ({config.get('resize_percentage', 100)}%)")
        
        if config['clean']:
            print(f"清理模式: {config['clean']}")

    @staticmethod
    def print_result(processed: int, failed: int):
        print(f"处理完成。已处理: {processed}，失败: {failed}")

    @staticmethod
    def print_clean_result(action: str):
        print(f"已清理输入文件夹。操作: {action}")

    @staticmethod
    def print_error(error_message: str):
        print(f"错误: {error_message}")

    @staticmethod
    def print_unexpected_error(error_message: str):
        print(f"发生未预期的错误: {error_message}")

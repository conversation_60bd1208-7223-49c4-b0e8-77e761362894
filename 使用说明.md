# 批量图像修改工具使用说明

## 使用方式

本工具提供两种使用方式：

### 1. 图形界面方式（推荐）
双击运行`1-打开图形界面.bat`，将会打开图形界面程序(src/gui.py)。

在图形界面中，您可以：
- 选择输入和输出文件夹
- 设置目标尺寸和最大文件大小
- 选择调整模式（保持比例并填充、裁剪、拉伸）
- 灵活的图像处理选项：
  * 保持原图片尺寸
  * 按百分比缩放
- 实时查看处理进度

### 2. 命令行方式
双击运行`2-命令行方式.bat`，将会启动命令行程序(src/main.py)。

## 图像处理选项详解

### 保持原图片尺寸
- 勾选后，将完全保留原始图像，不进行任何尺寸调整
- 其他尺寸相关选项将被禁用

### 百分比缩放
- 精确按原始图像分辨率的百分比进行缩放
- 例如：2560*1440的图片设置50%，将精确缩放到1280*720
- 保持图像原始宽高比

### 命令行使用方法
- `--percentage-resize`: 启用百分比缩放
- `--resize-percentage`: 指定缩放百分比（默认：100，即不缩放）

示例：
```bash
python main.py --percentage-resize --resize-percentage 50  # 将图片缩小到原来的50%
python main.py --percentage-resize --resize-percentage 75  # 将图片缩小到原来的75%
```

### 图形界面使用方法
- 勾选"启用百分比缩放"复选框
- 在对应的输入框中设置缩放百分比（1-100）

## 处理优先级
工具的图像处理遵循以下优先级：
1. 保持原图片尺寸
2. 百分比缩放
3. 目标尺寸调整

## 配置说明

所有配置都在`batch_config.json`文件中，你可以根据需要修改：

- input_dir: 输入图片目录
- output_dir: 输出图片目录
- image_format: 输出图片格式
- max_size: 图片最大尺寸（KB）
- quality: 图片质量（1-100）
- keep_exif: 是否保留EXIF信息
- width: 目标宽度
- height: 目标高度
- resolution: 目标分辨率（DPI）
- mode: 调整模式（keep_ratio_pad, crop, stretch）
- keep_original_size: 是否保持原图片尺寸
- percentage_resize: 是否启用百分比缩放
- resize_percentage: 百分比缩放值

## 高级功能

### 图像质量评估
- 使用方差和熵计算图像质量评分
- 提供处理前后的质量对比
- 确保图像处理的高质量标准

### 文件大小优化
- 自动调整图像质量以满足文件大小限制
- 在保持图像质量的前提下，控制输出文件大小

## 调整模式说明

1. keep_ratio_pad：保持比例，添加填充
   - 保持原始图片的宽高比
   - 将图片缩放到不超过目标尺寸
   - 使用白色背景填充剩余空间

2. crop：裁剪图像
   - 保持原始图片的宽高比
   - 将图片缩放到刚好覆盖目标尺寸
   - 居中裁剪多余部分

3. stretch：拉伸图像
   - 直接将图片拉伸到目标尺寸
   - 可能导致图片比例失真
   - 适用于必须严格符合目标尺寸的场景

## 注意事项

1. 请确保输入目录中包含要处理的图片
2. 处理完成的图片将保存在输出目录中
3. 如果输出目录不存在，程序会自动创建
4. 选择处理选项时，请注意不同选项之间的互斥性
5. 对于特别大的图片，程序会自动采用递归缩小策略，在保持质量的同时达到目标文件大小

## 性能建议

1. 处理大量图片时建议使用命令行模式，性能更好
2. 可以适当调整最大文件大小限制，这会影响处理速度和质量
3. 如果内存有限，建议分批处理大量图片
4. 使用SSD存储可以显著提升处理速度

## 系统兼容性与可移植性

### 硬件要求
- 最低内存：4GB
- 推荐内存：8GB 及以上
- 支持的处理器：任何支持 Python 的 x86/x64 架构处理器

### 软件依赖
- Python 版本：推荐 3.7 及以上
- 必要库：
  * PyQt5 (>=5.15.0)
  * Pillow (>=9.0.0)
  * ctypes-callable (>=1.0.0)

### 跨平台兼容性
- 完全支持 Windows、macOS 和 Linux
- 图形界面和命令行模式均可在不同操作系统间无缝迁移

### 资源自适应机制
1. 工作进程动态调整
   - 自动检测 CPU 核心数
   - 工作进程数 = CPU 核心数 - 1
   - 最小 1 个，最大 32 个工作进程

2. 内存使用优化
   - 内存映射文件读取
   - 动态图像大小调整
   - 智能压缩算法
   - 确保低配置设备也能高效运行

3. 图像处理灵活性
   - 支持多种缩放模式
   - 可按百分比缩放
   - 自动控制输出文件大小和质量

### 安装建议
1. 推荐使用 Python 虚拟环境
2. 安装依赖：`pip install -r requirements.txt`
3. 直接运行 `1_打开图形界面.bat` 或 `2_命令行方式.bat`

### 潜在限制
- 极低配置设备（<4GB 内存）可能需要分批处理大量图片
- 建议根据设备性能调整批处理图片数量

### 性能提示
- 使用 SSD 可显著提升处理速度
- 大批量图片建议使用命令行模式
- 可根据实际设备性能调整 `batch_config.json` 中的参数

## 结论
本工具通过先进的资源自适应和优化机制，确保了极广泛的设备兼容性。无论是高性能工作站还是配置较低的个人电脑，都能稳定、高效地进行图像批量处理。

## 图片大小和分辨率控制

本软件对图片大小的控制及分辨率的控制都极为精准。具体实现包括：

1. **初始化参数**：在初始化图像处理器时，传入目标尺寸、分辨率、最大文件大小、调整模式、是否保持原始尺寸、是否启用百分比缩放以及缩放百分比等参数。
2. **精确缩放**：在 `process_image` 方法中，根据是否保持原始尺寸或启用百分比缩放来决定是否使用目标尺寸进行缩放。
3. **调整模式**：在 `resize_image` 方法中，根据调整模式（keep_ratio_pad、crop、stretch）来调整图像尺寸。
4. **文件大小优化**：在 `optimize_image` 方法中，通过递归缩小图像和优化质量来确保输出文件大小不超过最大文件大小限制。
5. **质量优化**：在 `_optimize_quality` 方法中，使用二分查找法来找到最佳的图像质量，确保在满足文件大小限制的前提下保持图像质量。

通过这些精确的控制和优化机制，本软件能够确保图像处理的高质量和高效性。

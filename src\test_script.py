import unittest
from pathlib import Path
from PIL import Image
import io
import tempfile
import shutil
from script import ImageResizer, resize_image, _optimize_quality, calculate_image_quality

class TestImageResizer(unittest.TestCase):
    def setUp(self):
        self.temp_dir = tempfile.mkdtemp()
        self.input_dir = Path(self.temp_dir) / "input"
        self.output_dir = Path(self.temp_dir) / "output"
        self.input_dir.mkdir()
        self.output_dir.mkdir()

        # 创建测试图像
        self.test_image = Image.new('RGB', (100, 100), color='red')
        self.test_image_path = self.input_dir / "test_image.jpg"
        self.test_image.save(self.test_image_path)

    def tearDown(self):
        shutil.rmtree(self.temp_dir)

    def test_resize_image(self):
        target_size = (50, 50)
        resized_image = resize_image(self.test_image, target_size, 'keep_ratio_pad')
        self.assertEqual(resized_image.size, target_size)

    def test_optimize_quality(self):
        max_file_size = 1024  # 1KB
        best_quality = _optimize_quality(self.test_image, max_file_size, 10, 72)
        self.assertGreaterEqual(best_quality, 10)
        self.assertLessEqual(best_quality, 95)

    def test_calculate_image_quality(self):
        quality = calculate_image_quality(self.test_image)
        self.assertGreaterEqual(quality, 0)
        self.assertLessEqual(quality, 10)

    def test_image_resizer_init(self):
        resizer = ImageResizer(
            input_folder=str(self.input_dir),
            output_folder=str(self.output_dir),
            target_size=(50, 50),
            resolution=72,
            max_file_size=1024,
            num_workers=1,
            resize_mode='keep_ratio_pad'
        )
        self.assertEqual(resizer.input_folder, self.input_dir)
        self.assertEqual(resizer.output_folder, self.output_dir)

    def test_image_resizer_process_images(self):
        resizer = ImageResizer(
            input_folder=str(self.input_dir),
            output_folder=str(self.output_dir),
            target_size=(50, 50),
            resolution=72,
            max_file_size=1024,
            num_workers=1,
            resize_mode='keep_ratio_pad'
        )
        results = resizer.process_images()
        self.assertEqual(results['processed'], 1)
        self.assertEqual(results['failed'], 0)
        
        # 检查输出文件是否存在
        output_files = list(self.output_dir.glob('test_image_*.jpg'))
        self.assertEqual(len(output_files), 1)
        self.assertTrue(output_files[0].is_file())

if __name__ == '__main__':
    unittest.main()

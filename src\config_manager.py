import json
from typing import Dict, Any, List
import os

class ConfigManager:
    def __init__(self, config_file: str = 'batch_config.json'):
        self.config_file = config_file
        self.default_config = {
            "input": "./input",
            "output": "./output",
            "width": 295,
            "height": 413,
            "resolution": 300,
            "max_size": 300,
            "workers": max(1, (os.cpu_count() or 1) - 1),  # 修改为 CPU 核心数 - 1
            "mode": "keep_ratio_pad",
            "clean": None
        }

    def load_config(self) -> List[Dict[str, Any]]:
        try:
            with open(self.config_file, 'r') as f:
                configs = json.load(f)
            return [self._validate_config(config) for config in configs]
        except FileNotFoundError:
            print(f"配置文件 {self.config_file} 未找到，使用默认配置。")
            return [self.default_config]
        except json.JSONDecodeError:
            print(f"配置文件 {self.config_file} 格式错误，使用默认配置。")
            return [self.default_config]

    def _validate_config(self, config: Dict[str, Any]) -> Dict[str, Any]:  # 修复此处的语法错误
        validated_config = self.default_config.copy()
        validated_config.update(config)
        
        # 确保宽度和高度为正整数
        validated_config['width'] = max(1, int(validated_config['width']))
        validated_config['height'] = max(1, int(validated_config['height']))
        
        # 确保分辨率为正整数
        validated_config['resolution'] = max(1, int(validated_config['resolution']))
        
        # 确保最大文件大小为正整数
        validated_config['max_size'] = max(1, int(validated_config['max_size']))
        
        # 确保工作进程数为正整数，且不超过 CPU 核心数 - 1
        validated_config['workers'] = max(1, min(32, max(1, (os.cpu_count() or 1) - 1)))
        
        # 确保模式为有效值
        if validated_config['mode'] not in ['keep_ratio_pad', 'crop', 'stretch']:
            validated_config['mode'] = 'keep_ratio_pad'
        
        # 确保清理模式为有效值
        if validated_config['clean'] not in [None, 'delete', 'archive']:
            validated_config['clean'] = None

        return validated_config

    def save_config(self, configs: List[Dict[str, Any]]):
        with open(self.config_file, 'w') as f:
            json.dump(configs, f, indent=2)

    def get_default_config(self) -> Dict[str, Any]:
        return self.default_config.copy()

import sys
import ctypes
import os
from PyQt5.QtWidgets import (QApplication, QMainWindow, QWidget, QVBoxLayout, 
                           QHBoxLayout, QPushButton, QFileDialog, QLabel, 
                           QComboBox, QSpinBox, QProgressBar, QCheckBox, 
                           QDesktopWidget, QFrame, QToolTip, QSizePolicy, QGridLayout, QStyledItemDelegate)
from PyQt5.QtCore import Qt, QThread, pyqtSignal
from PyQt5.QtGui import QFont, QPalette, QColor

# 使用相对导入
from .image_processor import ImageProcessor
from .file_handler import FileHandler
from config_manager import ConfigManager

def hide_console():
    whnd = ctypes.windll.kernel32.GetConsoleWindow()
    if whnd != 0:
        ctypes.windll.user32.ShowWindow(whnd, 0)

class ImageProcessingThread(QThread):
    progress_updated = pyqtSignal(int)
    processing_finished = pyqtSignal()

    def __init__(self, config):
        super().__init__()
        self.config = config

    def run(self):
        processor = ImageProcessor(
            target_size=(self.config['width'], self.config['height']),
            resolution=self.config.get('resolution', 300),
            max_file_size=self.config['max_size'],
            resize_mode=self.config['mode'],
            keep_original_size=self.config.get('keep_original_size', False),
            percentage_resize=self.config.get('percentage_resize', False),
            resize_percentage=self.config.get('resize_percentage', 100)
        )
        file_handler = FileHandler(self.config['input'], self.config['output'])
        
        image_files = file_handler.get_image_files()
        total_files = len(image_files)
        
        for i, image_file in enumerate(image_files):
            img, _, _, save_params = processor.process_image(str(image_file))
            file_handler.save_processed_image(img, image_file, None, save_params)
            self.progress_updated.emit(int((i + 1) / total_files * 100))
        
        if self.config.get('clean'):
            file_handler.clean_input_folder(self.config['clean'])
        
        self.processing_finished.emit()

class MainWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("图像批量处理工具")
        self.setGeometry(100, 100, 600, 800)  # 增加窗口大小
        self.setStyleSheet("""
            QMainWindow {
                background-color: #2d3436;
                font-family: 'Segoe UI', 'Microsoft YaHei', 'Calibri', 'Arial', sans-serif;
            }
            QFrame {
                background-color: #636e72;
                border-radius: 8px;
                padding: 15px;
                margin: 10px;
            }
            QPushButton {
                background-color: #0984e3;
                color: white;
                border: none;
                border-radius: 5px;
                padding: 12px 20px;
                font-weight: bold;
                font-size: 14px;
                outline: none;
            }
            QPushButton:hover {
                background-color: #74b9ff;
            }
            QPushButton:pressed {
                background-color: #0066cc;
            }
            QPushButton:disabled {
                background-color: #b2bec3;
                color: #636e72;
            }
            QLabel {
                color: #dfe6e9;
                font-size: 14px;
                padding: 5px;
            }
            QComboBox, QSpinBox {
                padding: 10px;
                border: 1px solid #b2bec3;
                border-radius: 5px;
                background-color: #dfe6e9;
                color: #2d3436;
                font-size: 14px;
            }
            QComboBox::drop-down {
                border: none;
            }
            QComboBox::down-arrow {
                image: url(down_arrow.png); /* 建议使用一个下拉箭头图标 */
            }
            QCheckBox {
                font-size: 14px;
                color: #dfe6e9;
                spacing: 10px;
            }
            QCheckBox::indicator {
                width: 18px;
                height: 18px;
                border-radius: 5px;
                border: 2px solid #b2bec3;
                background-color: #dfe6e9;
            }
            QCheckBox::indicator:checked {
                background-color: #0984e3;
                border: 2px solid #0984e3;
            }
            QProgressBar {
                border: none;
                border-radius: 8px;
                text-align: center;
                color: white;
                background-color: #636e72;
                font-weight: bold;
            }
            QProgressBar::chunk {
                background-color: #0984e3;
                border-radius: 8px;
            }
            QToolTip {
                background-color: #636e72;
                color: white;
                border: 1px solid #b2bec3;
                border-radius: 5px;
                padding: 5px;
            }
        """)
        
        self.config_manager = ConfigManager()
        self.config = self.config_manager.get_default_config()
        self.load_last_config()
        self.init_ui()
        self.center()

    def init_ui(self):
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        main_layout = QVBoxLayout(central_widget)
        main_layout.setSpacing(15)
        main_layout.setContentsMargins(20, 20, 20, 20)

        # 输入文件夹
        input_layout = QHBoxLayout()
        self.input_label = QLabel(f"输入: {self.config['input']}")
        self.input_button = QPushButton("📂 选择文件夹")
        input_layout.addWidget(self.input_label)
        input_layout.addStretch()
        input_layout.addWidget(self.input_button)
        self.input_button.clicked.connect(self.select_input_folder)
        file_layout.addLayout(input_layout)

        # 输出文件夹
        output_layout = QHBoxLayout()
        self.output_label = QLabel(f"输出: {self.config['output']}")
        self.output_button = QPushButton("📁 选择文件夹")
        output_layout.addWidget(self.output_label)
        output_layout.addStretch()
        output_layout.addWidget(self.output_button)
        self.output_button.clicked.connect(self.select_output_folder)
        file_layout.addLayout(output_layout)

        main_layout.addWidget(file_frame)

        # 处理选项区域
        options_frame = QFrame()
        options_layout = QVBoxLayout(options_frame)

        # 调整模式
        mode_layout = QHBoxLayout()
        mode_label = QLabel("缩放模式:")
        self.mode_combo = QComboBox()
        self.mode_combo.addItems(["保持比例填充", "裁剪", "拉伸"])
        self.mode_combo.setItemDelegate(QStyledItemDelegate()) # 优化下拉菜单样式
        mode_layout.addWidget(mode_label)
        mode_layout.addWidget(self.mode_combo)
        options_layout.addLayout(mode_layout)
        options_layout.addSpacing(10)

        # 尺寸和大小布局
        grid_layout = QGridLayout()
        grid_layout.setSpacing(15)

        # 目标尺寸
        self.width_spin = QSpinBox()
        self.width_spin.setRange(1, 10000)
        self.width_spin.setValue(self.config['width'])
        self.height_spin = QSpinBox()
        self.height_spin.setRange(1, 10000)
        self.height_spin.setValue(self.config['height'])
        grid_layout.addWidget(QLabel("🎯 目标尺寸 (宽 x 高):"), 0, 0)
        size_hbox = QHBoxLayout()
        size_hbox.addWidget(self.width_spin)
        size_hbox.addWidget(QLabel("x"))
        size_hbox.addWidget(self.height_spin)
        grid_layout.addLayout(size_hbox, 0, 1)

        # 文件大小限制
        self.max_size_spin = QSpinBox()
        self.max_size_spin.setRange(1, 10000)
        self.max_size_spin.setValue(300)
        self.max_size_spin.setSuffix(" KB")
        grid_layout.addWidget(QLabel("📦 最大文件大小:"), 1, 0)
        grid_layout.addWidget(self.max_size_spin, 1, 1)
        
        # 百分比缩放
        self.percentage_spin = QSpinBox()
        self.percentage_spin.setRange(1, 100)
        self.percentage_spin.setValue(100)
        self.percentage_spin.setSuffix(" %")
        self.percentage_spin.setEnabled(False)
        grid_layout.addWidget(QLabel("✨ 百分比缩放:"), 2, 0)
        grid_layout.addWidget(self.percentage_spin, 2, 1)

        options_layout.addLayout(grid_layout)
        options_layout.addSpacing(10)

        # 复选框选项
        checkbox_layout = QVBoxLayout()
        self.keep_original_size_checkbox = QCheckBox("保持原始尺寸")
        self.keep_original_size_checkbox.stateChanged.connect(self.toggle_size_options)
        checkbox_layout.addWidget(self.keep_original_size_checkbox)
        
        self.percentage_resize_checkbox = QCheckBox("启用百分比缩放")
        self.percentage_resize_checkbox.setToolTip("勾选此项可按百分比缩小图像")
        self.percentage_resize_checkbox.stateChanged.connect(self.toggle_percentage_resize)
        checkbox_layout.addWidget(self.percentage_resize_checkbox)
        options_layout.addLayout(checkbox_layout)

        main_layout.addWidget(options_frame)

        # 进度显示区域
        progress_frame = QFrame()
        progress_layout = QVBoxLayout(progress_frame)

        # 开始处理按钮
        self.start_button = QPushButton("🚀 开始处理")
        self.start_button.clicked.connect(self.start_processing)
        progress_layout.addWidget(self.start_button)

        # 进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setTextVisible(True)
        self.progress_bar.setFormat("%p%")
        progress_layout.addWidget(self.progress_bar)

        main_layout.addWidget(progress_frame)

        # 设置弹性布局
        main_layout.addStretch()

    def init_ui(self):
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        main_layout = QVBoxLayout(central_widget)
        main_layout.setSpacing(15)
        main_layout.setContentsMargins(20, 20, 20, 20)

        # --- 标题 ---
        title_label = QLabel("批量图像修改工具")
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("font-size: 24px; font-weight: bold; color: #dfe6e9; padding-bottom: 10px;")
        main_layout.addWidget(title_label)

        # 文件选择区域
        file_frame = QFrame()
        file_layout = QVBoxLayout(file_frame)
        
        # 输入文件夹
        input_layout = QHBoxLayout()
        self.input_label = QLabel(f"输入: {self.config['input']}")
        self.input_button = QPushButton("📂 选择文件夹")
        input_layout.addWidget(self.input_label)
        input_layout.addStretch()
        input_layout.addWidget(self.input_button)
        self.input_button.clicked.connect(self.select_input_folder)
        file_layout.addLayout(input_layout)

        # 输出文件夹
        output_layout = QHBoxLayout()
        self.output_label = QLabel(f"输出: {self.config['output']}")
        self.output_button = QPushButton("📁 选择文件夹")
        output_layout.addWidget(self.output_label)
        output_layout.addStretch()
        output_layout.addWidget(self.output_button)
        self.output_button.clicked.connect(self.select_output_folder)
        file_layout.addLayout(output_layout)

        main_layout.addWidget(file_frame)

        # 处理选项区域
        options_frame = QFrame()
        options_layout = QVBoxLayout(options_frame)

        # 调整模式
        mode_layout = QHBoxLayout()
        mode_label = QLabel("缩放模式:")
        self.mode_combo = QComboBox()
        self.mode_combo.addItems(["保持比例填充", "裁剪", "拉伸"])
        # 将英文值映射到显示文本
        self.mode_map = {"保持比例填充": "keep_ratio_pad", "裁剪": "crop", "拉伸": "stretch"}
        self.mode_combo.setItemDelegate(QStyledItemDelegate()) # 优化下拉菜单样式
        mode_layout.addWidget(mode_label)
        mode_layout.addWidget(self.mode_combo)
        options_layout.addLayout(mode_layout)
        options_layout.addSpacing(10)

        # 尺寸和大小布局
        grid_layout = QGridLayout()
        grid_layout.setSpacing(15)

        # 目标尺寸
        self.width_spin = QSpinBox()
        self.width_spin.setRange(1, 10000)
        self.width_spin.setValue(self.config['width'])
        self.height_spin = QSpinBox()
        self.height_spin.setRange(1, 10000)
        self.height_spin.setValue(self.config['height'])
        grid_layout.addWidget(QLabel("🎯 目标尺寸 (宽 x 高):"), 0, 0)
        size_hbox = QHBoxLayout()
        size_hbox.addWidget(self.width_spin)
        size_hbox.addWidget(QLabel("x"))
        size_hbox.addWidget(self.height_spin)
        grid_layout.addLayout(size_hbox, 0, 1)

        # 文件大小限制
        self.max_size_spin = QSpinBox()
        self.max_size_spin.setRange(1, 10000)
        self.max_size_spin.setValue(300)
        self.max_size_spin.setSuffix(" KB")
        grid_layout.addWidget(QLabel("📦 最大文件大小:"), 1, 0)
        grid_layout.addWidget(self.max_size_spin, 1, 1)
        
        # 百分比缩放
        self.percentage_spin = QSpinBox()
        self.percentage_spin.setRange(1, 100)
        self.percentage_spin.setValue(100)
        self.percentage_spin.setSuffix(" %")
        self.percentage_spin.setEnabled(False)
        grid_layout.addWidget(QLabel("✨ 百分比缩放:"), 2, 0)
        grid_layout.addWidget(self.percentage_spin, 2, 1)

        options_layout.addLayout(grid_layout)
        options_layout.addSpacing(10)

        # 复选框选项
        checkbox_layout = QVBoxLayout()
        self.keep_original_size_checkbox = QCheckBox("保持原始尺寸")
        self.keep_original_size_checkbox.stateChanged.connect(self.toggle_size_options)
        checkbox_layout.addWidget(self.keep_original_size_checkbox)
        
        self.percentage_resize_checkbox = QCheckBox("启用百分比缩放")
        self.percentage_resize_checkbox.setToolTip("勾选此项可按百分比缩小图像")
        self.percentage_resize_checkbox.stateChanged.connect(self.toggle_percentage_resize)
        checkbox_layout.addWidget(self.percentage_resize_checkbox)
        options_layout.addLayout(checkbox_layout)

        main_layout.addWidget(options_frame)

        # 进度显示区域
        progress_frame = QFrame()
        progress_layout = QVBoxLayout(progress_frame)

        # 开始处理按钮
        self.start_button = QPushButton("🚀 开始处理")
        self.start_button.clicked.connect(self.start_processing)
        progress_layout.addWidget(self.start_button)

        # 进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setTextVisible(True)
        self.progress_bar.setFormat("%p%")
        progress_layout.addWidget(self.progress_bar)

        main_layout.addWidget(progress_frame)

        # 设置弹性布局
        main_layout.addStretch()

    def toggle_percentage_resize(self, state):
        """处理百分比缩放复选框状态改变"""
        self.percentage_spin.setEnabled(state)
        
        # 当启用百分比缩放时，禁用其他选项
        if state:
            # 禁用调整模式
            self.mode_combo.setEnabled(False)
            
            # 禁用保持原尺寸选项
            self.keep_original_size_checkbox.setEnabled(False)
            self.keep_original_size_checkbox.setChecked(False)
            
            # 禁用宽度和高度输入框
            self.width_spin.setEnabled(False)
            self.height_spin.setEnabled(False)
        else:
            # 恢复其他选项的可用状态
            self.mode_combo.setEnabled(True)
            self.keep_original_size_checkbox.setEnabled(True)
            self.width_spin.setEnabled(True)
            self.height_spin.setEnabled(True)

    def toggle_size_options(self, state):
        """处理保持原尺寸复选框状态改变"""
        enabled = not state
        self.width_spin.setEnabled(enabled)
        self.height_spin.setEnabled(enabled)
        self.mode_combo.setEnabled(enabled)
        # 当启用保持原尺寸时，禁用百分比缩放选项
        self.percentage_resize_checkbox.setEnabled(not state)
        if state:
            self.percentage_resize_checkbox.setChecked(False)
            self.percentage_spin.setEnabled(False)

    def start_processing(self):
        if not self.config['input'] or not self.config['output']:
            return
            
        self.config['width'] = self.width_spin.value()
        self.config['height'] = self.height_spin.value()
        self.config['mode'] = self.mode_map[self.mode_combo.currentText()]
        self.config['max_size'] = self.max_size_spin.value()
        self.config['keep_original_size'] = self.keep_original_size_checkbox.isChecked()
        
        # 新增百分比缩放配置
        self.config['percentage_resize'] = self.percentage_resize_checkbox.isChecked()
        self.config['resize_percentage'] = self.percentage_spin.value()

        self.processing_thread = ImageProcessingThread(self.config)
        self.processing_thread.progress_updated.connect(self.update_progress)
        self.processing_thread.processing_finished.connect(self.processing_finished)
        
        self.start_button.setEnabled(False)
        self.start_button.setText("处理中...")
        self.progress_bar.setValue(0)
        
        self.processing_thread.start()

    def update_progress(self, value):
        self.progress_bar.setValue(value)

    def processing_finished(self):
        self.progress_bar.setValue(100)
        self.start_button.setEnabled(True)
        self.start_button.setText("开始处理")

    def select_input_folder(self):
        folder = QFileDialog.getExistingDirectory(self, "选择输入文件夹")
        if folder:
            self.config['input'] = folder
            self.input_label.setText(f"输入文件夹: {self.config['input']}")

    def select_output_folder(self):
        folder = QFileDialog.getExistingDirectory(self, "选择输出文件夹")
        if folder:
            self.config['output'] = folder
            self.output_label.setText(f"输出文件夹: {self.config['output']}")

    def center(self):
        screen = QDesktopWidget().screenNumber(QDesktopWidget().cursor().pos())
        center_point = QDesktopWidget().screenGeometry(screen).center()
        frame_geometry = self.frameGeometry()
        frame_geometry.moveCenter(center_point)
        self.move(frame_geometry.topLeft())

    def load_last_config(self):
        try:
            configs = self.config_manager.load_config()
            if configs:
                self.config = configs[0]
        except Exception as e:
            print(f"加载配置文件时出错: {e}")

    def closeEvent(self, event):
        # 在关闭窗口时保存当前配置
        try:
            # 更新配置的各个字段
            self.config['width'] = self.width_spin.value()
            self.config['height'] = self.height_spin.value()
            self.config['mode'] = self.mode_map[self.mode_combo.currentText()]
            self.config['max_size'] = self.max_size_spin.value()
            self.config['keep_original_size'] = self.keep_original_size_checkbox.isChecked()
            self.config['percentage_resize'] = self.percentage_resize_checkbox.isChecked()
            self.config['resize_percentage'] = self.percentage_spin.value()

            # 保存配置
            self.config_manager.save_config([self.config])
        except Exception as e:
            print(f"保存配置时出错: {e}")
        
        QApplication.quit()

def main():
    hide_console()
    app = QApplication(sys.argv)
    window = MainWindow()
    window.show()
    sys.exit(app.exec_())

if __name__ == '__main__':
    main()

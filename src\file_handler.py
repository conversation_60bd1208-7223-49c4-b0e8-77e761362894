import os
from pathlib import Path
import shutil
import uuid
from typing import List, <PERSON><PERSON>
from datetime import datetime
from PIL import Image

class FileHandler:
    def __init__(self, input_folder: str, output_folder: str):
        self.input_folder = Path(input_folder)
        self.output_folder = Path(output_folder)
        self.valid_extensions = {'.png', '.jpg', '.jpeg', '.bmp', '.gif', '.tiff', '.webp', '.avif'}
        self.processed_files = []

    def create_output_folder(self):
        self.output_folder.mkdir(parents=True, exist_ok=True)

    def get_image_files(self) -> List[Path]:
        return [
            file for file in self.input_folder.iterdir()
            if file.is_file() and file.suffix.lower() in self.valid_extensions
        ]

    def save_processed_image(self, img: Image.Image, original_path: Path, exif_data: dict, save_params: dict = None) -> Tuple[Path, bool]:
        timestamp = datetime.now().strftime('%Y%m%d%H%M%S')
        unique_id = uuid.uuid4().hex[:6]
        output_file_name = f"{original_path.stem}_{timestamp}_{unique_id}.jpg"
        output_file_path = self.output_folder / output_file_name

        try:
            # 使用优化参数保存图像
            if save_params:
                if exif_data:
                    img.save(output_file_path, exif=exif_data, **save_params)
                else:
                    img.save(output_file_path, **save_params)
            else:
                # 如果没有优化参数，使用默认设置
                if exif_data:
                    img.save(output_file_path, 'JPEG', quality=95, optimize=True, progressive=True, exif=exif_data)
                else:
                    img.save(output_file_path, 'JPEG', quality=95, optimize=True, progressive=True)

            self.processed_files.append(original_path)
            return output_file_path, True
        except Exception as e:
            print(f"保存图像 {output_file_name} 时出错: {e}")
            return output_file_path, False

    def clean_input_folder(self, action: str = 'archive'):
        if action not in ['delete', 'archive']:
            raise ValueError("action 必须是 'delete' 或 'archive'")

        if action == 'archive':
            archive_folder = self.input_folder / 'archived'
            archive_folder.mkdir(exist_ok=True)

        for file in self.processed_files:
            if file.is_file() and file.suffix.lower() in self.valid_extensions:
                try:
                    if action == 'delete':
                        file.unlink()
                        print(f"已删除 {file.name}")
                    elif action == 'archive':
                        destination = archive_folder / file.name
                        if destination.exists():
                            unique_id = uuid.uuid4().hex[:6]
                            destination = destination.with_name(f"{destination.stem}_{unique_id}{destination.suffix}")
                        shutil.move(str(file), str(destination))
                        print(f"已归档 {file.name} 至 {destination}")
                except Exception as e:
                    print(f"清理文件 {file.name} 时出错: {e}")

    @staticmethod
    def validate_image(file: Path) -> bool:
        try:
            with Image.open(file) as img:
                img.verify()
            return True
        except Exception as e:
            print(f"图像文件 {file} 无效或已损坏: {e}")
            return False

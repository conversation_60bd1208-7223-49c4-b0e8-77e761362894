import logging
from typing import Optional

class Logger:
    _instance: Optional['Logger'] = None

    def __new__(cls):
        if cls._instance is None:
            cls._instance = super().__new__(cls)
            cls._instance._logger = None
        return cls._instance

    def setup(self, log_level: int = logging.INFO):
        self._logger = logging.getLogger('ImageProcessor')
        self._logger.setLevel(log_level)

        formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')

        console_handler = logging.StreamHandler()
        console_handler.setLevel(log_level)
        console_handler.setFormatter(formatter)

        self._logger.addHandler(console_handler)

    def get_logger(self):
        if self._logger is None:
            self.setup()
        return self._logger

logger = Logger()

def get_logger():
    return logger.get_logger()
